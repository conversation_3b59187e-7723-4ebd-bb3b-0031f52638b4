<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

class Dashboard extends Component
{
    public $totalProducts = 0;
    public $activeProducts = 0;
    public $lowStockProducts = 0;
    public $totalValue = 0;
    public $recentProducts = [];

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $this->totalProducts = Product::count();
        $this->activeProducts = Product::active()->count();
        $this->lowStockProducts = Product::where('stock', '<=', 10)->count();
        $this->totalValue = Product::sum(DB::raw('price * stock'));
        $this->recentProducts = Product::latest()->take(5)->get();
    }

    public function refreshData()
    {
        $this->loadDashboardData();
        $this->dispatch('dashboard-refreshed');
    }

    public function render()
    {
        return view('livewire.dashboard')
            ->layout('layouts.app', [
                'title' => 'Dashboard - ASICS'
            ]);
    }
}
