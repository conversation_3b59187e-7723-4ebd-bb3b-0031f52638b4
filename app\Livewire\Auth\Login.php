<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;

class Login extends Component
{
    #[Validate('required|string')]
    public $username = '';

    #[Validate('required|string')]
    public $password = '';

    public $remember = false;

    public function login()
    {
        $this->validate();

        $credentials = [
            'username' => $this->username,
            'password' => $this->password,
        ];

        if (Auth::attempt($credentials, $this->remember)) {
            session()->regenerate();

            session()->flash('success', 'Login successful!');

            return $this->redirect('/dashboard', navigate: true);
        }

        $this->addError('username', 'Invalid credentials');
        $this->reset('password');
    }

    public function render()
    {
        return view('livewire.auth.login')
            ->layout('layouts.guest');
    }
}
