<?php

namespace App\Livewire\Products;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Product;
use Livewire\Attributes\Url;

class ProductList extends Component
{
    use WithPagination;

    #[Url(as: 'search')]
    public $search = '';

    #[Url(as: 'category')]
    public $category = '';

    #[Url(as: 'status')]
    public $status = '';

    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;

    protected $paginationTheme = 'bootstrap';

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategory()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function deleteProduct($productId)
    {
        $product = Product::find($productId);
        if ($product) {
            $product->delete();
            session()->flash('success', 'Product deleted successfully!');
            $this->dispatch('product-deleted');
        }
    }

    public function toggleStatus($productId)
    {
        $product = Product::find($productId);
        if ($product) {
            $product->update(['is_active' => !$product->is_active]);
            session()->flash('success', 'Product status updated successfully!');
        }
    }

    public function clearFilters()
    {
        $this->reset(['search', 'category', 'status']);
        $this->resetPage();
    }

    public function render()
    {
        $query = Product::query();

        // Search functionality
        if ($this->search) {
            $query->search($this->search);
        }

        // Filter by category
        if ($this->category) {
            $query->where('category', $this->category);
        }

        // Filter by status
        if ($this->status !== '') {
            $query->where('is_active', $this->status);
        }

        $products = $query->orderBy($this->sortBy, $this->sortDirection)
                         ->paginate($this->perPage);

        $categories = Product::distinct()->pluck('category')->filter();

        return view('livewire.products.product-list', [
            'products' => $products,
            'categories' => $categories
        ])->layout('layouts.app', [
            'title' => 'Products - ASICS'
        ]);
    }
}
