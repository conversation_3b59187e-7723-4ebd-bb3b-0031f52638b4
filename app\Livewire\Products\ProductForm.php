<?php

namespace App\Livewire\Products;

use Livewire\Component;
use App\Models\Product;
use Livewire\Attributes\Validate;

class ProductForm extends Component
{
    public ?Product $product = null;
    public $isEdit = false;

    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('nullable|string')]
    public $description = '';

    #[Validate('required|numeric|min:0')]
    public $price = '';

    #[Validate('required|integer|min:0')]
    public $stock = '';

    #[Validate('required|string|max:100')]
    public $category = '';

    #[Validate('nullable|string|max:50')]
    public $barcode = '';

    public $is_active = true;

    public function mount(?Product $product = null)
    {
        if ($product && $product->exists) {
            $this->product = $product;
            $this->isEdit = true;
            $this->fill($product->toArray());
        } else {
            $this->generateBarcode();
        }
    }

    public function generateBarcode()
    {
        $this->barcode = substr(time(), -8);
    }

    public function updatedName()
    {
        // Auto-generate barcode when name changes if barcode is empty
        if (empty($this->barcode) && !$this->isEdit) {
            $this->generateBarcode();
        }
    }

    public function updatedPrice()
    {
        // Ensure price is not negative
        if ($this->price < 0) {
            $this->price = 0;
        }
    }

    public function updatedStock()
    {
        // Ensure stock is not negative
        if ($this->stock < 0) {
            $this->stock = 0;
        }
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'stock' => $this->stock,
            'category' => $this->category,
            'barcode' => $this->barcode ?: null,
            'is_active' => $this->is_active,
        ];

        if ($this->isEdit) {
            // Update existing product
            $this->product->update($data);
            session()->flash('success', 'Product updated successfully!');
        } else {
            // Create new product
            Product::create($data);
            session()->flash('success', 'Product created successfully!');
        }

        return $this->redirect('/products', navigate: true);
    }

    public function cancel()
    {
        return $this->redirect('/products', navigate: true);
    }

    public function render()
    {
        $title = $this->isEdit ? 'Edit Product' : 'Add New Product';

        return view('livewire.products.product-form')
            ->layout('layouts.app', [
                'title' => $title . ' - ASICS'
            ]);
    }
}
