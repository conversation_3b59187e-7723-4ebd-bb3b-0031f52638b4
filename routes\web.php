<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Livewire\Auth\Login;
use App\Livewire\Dashboard;
use App\Livewire\Products\ProductList;
use App\Livewire\Products\ProductForm;

// Redirect root to login
Route::get('/', function () {
    return redirect('/login');
});

// Authentication routes
Route::get('/login', Login::class)->name('login');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware('auth')->group(function () {
    // Dashboard
    Route::get('/dashboard', Dashboard::class)->name('dashboard');

    // Products CRUD using Livewire
    Route::get('/products', ProductList::class)->name('products.index');
    Route::get('/products/create', ProductForm::class)->name('products.create');
    Route::get('/products/{product}/edit', ProductForm::class)->name('products.edit');

    // Inventory Management Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/stock', function () {
            return view('inventory.stock')->layout('layouts.app');
        })->name('stock');
        Route::get('/reports', function () {
            return view('inventory.reports')->layout('layouts.app');
        })->name('reports');
    });

    // Sales Management Routes
    Route::prefix('sales')->name('sales.')->group(function () {
        Route::get('/pos', function () {
            return view('sales.pos')->layout('layouts.app');
        })->name('pos');
        Route::get('/history', function () {
            return view('sales.history')->layout('layouts.app');
        })->name('history');
        Route::get('/returns', function () {
            return view('sales.returns')->layout('layouts.app');
        })->name('returns');
        Route::get('/reports', function () {
            return view('sales.reports')->layout('layouts.app');
        })->name('reports');
    });

    // Customer Management Routes
    Route::prefix('customers')->name('customers.')->group(function () {
        Route::get('/', function () {
            return view('customers.index')->layout('layouts.app');
        })->name('index');
        Route::get('/create', function () {
            return view('customers.create')->layout('layouts.app');
        })->name('create');
        Route::get('/loyalty', function () {
            return view('customers.loyalty')->layout('layouts.app');
        })->name('loyalty');
    });

    // Reports Routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/daily', function () {
            return view('reports.daily')->layout('layouts.app');
        })->name('daily');
        Route::get('/weekly', function () {
            return view('reports.weekly')->layout('layouts.app');
        })->name('weekly');
        Route::get('/monthly', function () {
            return view('reports.monthly')->layout('layouts.app');
        })->name('monthly');
        Route::get('/export', function () {
            return view('reports.export')->layout('layouts.app');
        })->name('export');
    });

    // Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/store', function () {
            return view('settings.store')->layout('layouts.app');
        })->name('store');
        Route::get('/users', function () {
            return view('settings.users')->layout('layouts.app');
        })->name('users');
        Route::get('/printer', function () {
            return view('settings.printer')->layout('layouts.app');
        })->name('printer');
        Route::get('/backup', function () {
            return view('settings.backup')->layout('layouts.app');
        })->name('backup');
    });
});
