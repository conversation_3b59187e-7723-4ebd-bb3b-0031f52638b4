<div class="login-card">
    <div class="login-header">
        <i class="fas fa-running fa-3x mb-3"></i>
        <h3>ASICS</h3>
        <p class="mb-0">Please sign in to continue</p>
    </div>
    <div class="login-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <form wire:submit="login">
            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="fas fa-user me-2"></i>Username
                </label>
                <input type="text"
                       class="form-control @error('username') is-invalid @enderror"
                       id="username"
                       wire:model="username"
                       required
                       autofocus>
                @error('username')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password"
                       class="form-control @error('password') is-invalid @enderror"
                       id="password"
                       wire:model="password"
                       required>
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox"
                       class="form-check-input"
                       id="remember"
                       wire:model="remember">
                <label class="form-check-label" for="remember">
                    Remember me
                </label>
            </div>

            <div class="d-grid">
                <button type="submit"
                        class="btn btn-primary btn-login"
                        wire:loading.attr="disabled">
                    <span wire:loading.remove>
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </span>
                    <span wire:loading>
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Signing In...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>
