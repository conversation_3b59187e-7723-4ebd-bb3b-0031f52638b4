<div>
    <!-- <PERSON>er -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><i class="fas fa-box me-2"></i>Products</h4>
        </div>
        <div class="col-md-6 text-end">
            <button wire:click="clearFilters" class="btn btn-outline-secondary me-2">
                <i class="fas fa-times me-1"></i>Clear Filters
            </button>
            <a href="/products/create" wire:navigate class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New Product
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text"
                           class="form-control"
                           id="search"
                           wire:model.live.debounce.300ms="search"
                           placeholder="Search by name, barcode...">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" wire:model.live="category">
                        <option value="">All Categories</option>
                        @foreach($categories as $cat)
                            <option value="{{ $cat }}">{{ $cat }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" wire:model.live="status">
                        <option value="">All Status</option>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="perPage" class="form-label">Per Page</label>
                    <select class="form-select" id="perPage" wire:model.live="perPage">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            @if($products->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <button class="btn btn-link p-0 text-decoration-none"
                                            wire:click="sortBy('name')">
                                        Product
                                        @if($sortBy === 'name')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>Category</th>
                                <th>
                                    <button class="btn btn-link p-0 text-decoration-none"
                                            wire:click="sortBy('price')">
                                        Price
                                        @if($sortBy === 'price')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>
                                    <button class="btn btn-link p-0 text-decoration-none"
                                            wire:click="sortBy('stock')">
                                        Stock
                                        @if($sortBy === 'stock')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </button>
                                </th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $product)
                            <tr wire:key="product-{{ $product->id }}">
                                <td>
                                    <div>
                                        <strong>{{ $product->name }}</strong>
                                        @if($product->barcode)
                                            <br><small class="text-muted">{{ $product->barcode }}</small>
                                        @endif
                                        @if($product->description)
                                            <br><small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $product->category }}</span>
                                </td>
                                <td>{{ $product->formatted_price }}</td>
                                <td>
                                    <span class="badge {{ $product->stock <= 10 ? 'bg-warning text-dark' : 'bg-success' }}">
                                        {{ $product->stock }}
                                        @if($product->stock <= 10)
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                        @endif
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {{ $product->is_active ? 'bg-success' : 'bg-secondary' }}">
                                        {{ $product->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/products/{{ $product->id }}/edit"
                                           wire:navigate
                                           class="btn btn-sm btn-outline-primary"
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-{{ $product->is_active ? 'warning' : 'success' }}"
                                                wire:click="toggleStatus({{ $product->id }})"
                                                title="{{ $product->is_active ? 'Deactivate' : 'Activate' }}">
                                            <i class="fas fa-{{ $product->is_active ? 'eye-slash' : 'eye' }}"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger"
                                                wire:click="deleteProduct({{ $product->id }})"
                                                wire:confirm="Are you sure you want to delete this product?"
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing {{ $products->firstItem() }} to {{ $products->lastItem() }} of {{ $products->total() }} results
                    </div>
                    <div>
                        {{ $products->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-box fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No products found</h5>
                    <p class="text-muted">Try adjusting your search criteria or add a new product.</p>
                    <a href="/products/create" wire:navigate class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add First Product
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Loading indicator -->
    <div wire:loading.delay class="position-fixed top-50 start-50 translate-middle">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>
