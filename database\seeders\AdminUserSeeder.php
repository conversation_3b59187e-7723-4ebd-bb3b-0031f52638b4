<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if admin user already exists
        $existingUser = User::where('username', 'admin')->first();

        if ($existingUser) {
            $this->command->info('Admin user already exists. Updating password...');

            // Generate new salt and hash password
            $salt = User::generateSalt();
            $hashedPassword = User::hashPassword('pwd123', $salt);

            $existingUser->update([
                'password' => $hashedPassword,
                'salt' => $salt,
                'is_active' => true,
            ]);

            $this->command->info('Admin user password updated successfully.');
        } else {
            // Create new admin user
            $salt = User::generateSalt();
            $hashedPassword = User::hashPassword('pwd123', $salt);

            User::create([
                'username' => 'admin',
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => $hashedPassword,
                'salt' => $salt,
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $this->command->info('Admin user created successfully.');
        }

        $this->command->info('Admin credentials:');
        $this->command->info('Username: admin');
        $this->command->info('Password: pwd123');
    }
}
