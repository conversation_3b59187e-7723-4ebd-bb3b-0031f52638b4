@extends('layouts.app')

@section('title', 'Product Details - POS Kasir')
@section('page-title', 'Product Details')

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Product Details</h5>
                <div>
                    <a href="{{ route('products.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Products
                    </a>
                    <a href="{{ route('products.edit', $product) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="mb-3">{{ $product->name }}</h3>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Category:</label>
                            <span class="badge bg-info fs-6">{{ $product->category }}</span>
                        </div>

                        @if($product->description)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Description:</label>
                            <p class="text-muted">{{ $product->description }}</p>
                        </div>
                        @endif

                        <div class="mb-3">
                            <label class="form-label fw-bold">Price:</label>
                            <h4 class="text-primary">{{ $product->formatted_price }}</h4>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Stock:</label>
                            <span class="badge {{ $product->stock <= 10 ? 'bg-warning text-dark' : 'bg-success' }} fs-6">
                                {{ $product->stock }} units
                                @if($product->stock <= 10)
                                    <i class="fas fa-exclamation-triangle ms-1"></i>
                                @endif
                            </span>
                        </div>

                        @if($product->barcode)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Barcode:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" value="{{ $product->barcode }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyBarcode()">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3">
                            <label class="form-label fw-bold">Status:</label>
                            <span class="badge {{ $product->is_active ? 'bg-success' : 'bg-secondary' }} fs-6">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Product Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Product ID:</strong></div>
                                    <div class="col-6">#{{ $product->id }}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Created:</strong></div>
                                    <div class="col-6">{{ $product->created_at->format('d M Y, H:i') }}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Last Updated:</strong></div>
                                    <div class="col-6">{{ $product->updated_at->format('d M Y, H:i') }}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Total Value:</strong></div>
                                    <div class="col-6">
                                        <strong>Rp {{ number_format($product->price * $product->stock, 0, ',', '.') }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($product->stock <= 10)
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Low Stock Alert!</strong><br>
                            This product has low stock ({{ $product->stock }} units remaining).
                        </div>
                        @endif

                        @if(!$product->is_active)
                        <div class="alert alert-secondary mt-3">
                            <i class="fas fa-eye-slash me-2"></i>
                            <strong>Inactive Product</strong><br>
                            This product is currently inactive and won't appear in sales.
                        </div>
                        @endif
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteProduct()">
                            <i class="fas fa-trash me-2"></i>Delete Product
                        </button>
                    </div>
                    <div>
                        <a href="{{ route('products.index') }}" class="btn btn-secondary me-2">
                            <i class="fas fa-list me-2"></i>All Products
                        </a>
                        <a href="{{ route('products.edit', $product) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Product
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ $product->name }}</strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('products.destroy', $product) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Product</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteProduct() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function copyBarcode() {
    const barcodeInput = document.querySelector('input[value="{{ $product->barcode }}"]');
    barcodeInput.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}
</script>
@endpush
