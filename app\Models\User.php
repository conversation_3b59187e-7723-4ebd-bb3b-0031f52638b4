<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'salt',
        'name',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'salt',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Verify password using SQL Server stored procedure
     *
     * @param string $password
     * @return bool
     */
    public function verifyPassword($password)
    {
        try {
            $result = DB::select('EXEC sp_CheckLogin ?, ?', [
                $this->username,
                $password
            ]);

            return !empty($result) && $result[0]->IsValid == 1;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Find user by username for authentication
     *
     * @param string $username
     * @return User|null
     */
    public static function findByUsername($username)
    {
        return static::where('username', $username)
                    ->where('is_active', true)
                    ->first();
    }

    /**
     * Generate salt for password hashing
     *
     * @return string
     */
    public static function generateSalt()
    {
        return bin2hex(random_bytes(16));
    }

    /**
     * Hash password with salt using SQL Server method
     *
     * @param string $password
     * @param string $salt
     * @return string
     */
    public static function hashPassword($password, $salt)
    {
        // This mimics the SQL Server HASHBYTES('SHA1', @Password+CAST(Salt AS NVARCHAR(36)))
        return hash('sha1', $password . $salt);
    }
}
