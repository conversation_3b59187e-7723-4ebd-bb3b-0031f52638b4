<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'ASICS')</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    @livewireStyles
    <style>
        .sidebar {
            width: 250px !important;
            min-height: 100vh;
            max-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            display: block !important;
            visibility: visible !important;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        .sidebar .nav-link.has-submenu::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            transition: transform 0.3s;
        }
        .sidebar .nav-link.has-submenu[aria-expanded="false"]::after {
            transform: rotate(-90deg);
        }
        .sidebar .collapse {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin: 5px 0;
        }
        .sidebar .submenu .nav-link {
            padding: 8px 20px 8px 40px;
            margin: 1px 0;
            font-size: 0.9em;
        }
        .sidebar .submenu .nav-link:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(3px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 250px;
            width: calc(100% - 250px);
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .sidebar-toggle {
            display: none;
        }

        /* Ensure full width layout */
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .layout-wrapper {
            min-height: 100vh;
        }

        @media (min-width: 769px) {
            .sidebar {
                transform: translateX(0) !important;
                display: block !important;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                width: 100%;
            }
            .sidebar-toggle {
                display: block;
            }
        }
    </style>
    @stack('styles')
</head>
<body>
    <div class="layout-wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
        <div class="p-3 text-center text-white">
            <h4><i class="fas fa-running me-2"></i>ASICS</h4>
            <small>Welcome, {{ Auth::user()->name ?? Auth::user()->username }}</small>
        </div>
        <nav class="nav flex-column px-3 pb-4">
            <!-- Dashboard -->
            <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
               href="{{ route('dashboard') }}"
               wire:navigate>
                <span><i class="fas fa-tachometer-alt me-2"></i>Dashboard</span>
            </a>

            <!-- Inventory Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               href="#inventorySubmenu"
               role="button"
               aria-expanded="{{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? 'true' : 'false' }}"
               aria-controls="inventorySubmenu">
                <span><i class="fas fa-boxes me-2"></i>Inventory</span>
            </a>
            <div class="collapse {{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? 'show' : '' }}" id="inventorySubmenu">
                <a class="nav-link {{ request()->routeIs('products.index') ? 'active' : '' }}"
                   href="{{ route('products.index') }}"
                   wire:navigate>
                    <i class="fas fa-box me-2"></i>Products
                </a>
                <a class="nav-link {{ request()->routeIs('products.create') ? 'active' : '' }}"
                   href="{{ route('products.create') }}"
                   wire:navigate>
                    <i class="fas fa-plus me-2"></i>Add Product
                </a>
                <a class="nav-link {{ request()->routeIs('inventory.stock') ? 'active' : '' }}"
                   href="{{ route('inventory.stock') }}"
                   wire:navigate>
                    <i class="fas fa-warehouse me-2"></i>Stock Management
                </a>
                <a class="nav-link {{ request()->routeIs('inventory.reports') ? 'active' : '' }}"
                   href="{{ route('inventory.reports') }}"
                   wire:navigate>
                    <i class="fas fa-chart-line me-2"></i>Stock Reports
                </a>
            </div>

            <!-- Sales Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('sales.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               href="#salesSubmenu"
               role="button"
               aria-expanded="{{ request()->routeIs('sales.*') ? 'true' : 'false' }}"
               aria-controls="salesSubmenu">
                <span><i class="fas fa-shopping-cart me-2"></i>Sales</span>
            </a>
            <div class="collapse {{ request()->routeIs('sales.*') ? 'show' : '' }}" id="salesSubmenu">
                <a class="nav-link {{ request()->routeIs('sales.pos') ? 'active' : '' }}"
                   href="{{ route('sales.pos') }}"
                   wire:navigate>
                    <i class="fas fa-cash-register me-2"></i>Point of Sale
                </a>
                <a class="nav-link {{ request()->routeIs('sales.history') ? 'active' : '' }}"
                   href="{{ route('sales.history') }}"
                   wire:navigate>
                    <i class="fas fa-receipt me-2"></i>Sales History
                </a>
                <a class="nav-link {{ request()->routeIs('sales.returns') ? 'active' : '' }}"
                   href="{{ route('sales.returns') }}"
                   wire:navigate>
                    <i class="fas fa-undo me-2"></i>Returns
                </a>
                <a class="nav-link {{ request()->routeIs('sales.reports') ? 'active' : '' }}"
                   href="{{ route('sales.reports') }}"
                   wire:navigate>
                    <i class="fas fa-chart-bar me-2"></i>Sales Reports
                </a>
            </div>

            <!-- Customer Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('customers.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               href="#customersSubmenu"
               role="button"
               aria-expanded="{{ request()->routeIs('customers.*') ? 'true' : 'false' }}"
               aria-controls="customersSubmenu">
                <span><i class="fas fa-users me-2"></i>Customers</span>
            </a>
            <div class="collapse {{ request()->routeIs('customers.*') ? 'show' : '' }}" id="customersSubmenu">
                <a class="nav-link {{ request()->routeIs('customers.create') ? 'active' : '' }}"
                   href="{{ route('customers.create') }}"
                   wire:navigate>
                    <i class="fas fa-user-plus me-2"></i>Add Customer
                </a>
                <a class="nav-link {{ request()->routeIs('customers.index') ? 'active' : '' }}"
                   href="{{ route('customers.index') }}"
                   wire:navigate>
                    <i class="fas fa-list me-2"></i>Customer List
                </a>
                <a class="nav-link {{ request()->routeIs('customers.loyalty') ? 'active' : '' }}"
                   href="{{ route('customers.loyalty') }}"
                   wire:navigate>
                    <i class="fas fa-star me-2"></i>Loyalty Program
                </a>
            </div>

            <!-- Reports -->
            <a class="nav-link has-submenu {{ request()->routeIs('reports.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               href="#reportsSubmenu"
               role="button"
               aria-expanded="{{ request()->routeIs('reports.*') ? 'true' : 'false' }}"
               aria-controls="reportsSubmenu">
                <span><i class="fas fa-chart-pie me-2"></i>Reports</span>
            </a>
            <div class="collapse {{ request()->routeIs('reports.*') ? 'show' : '' }}" id="reportsSubmenu">
                <a class="nav-link {{ request()->routeIs('reports.daily') ? 'active' : '' }}"
                   href="{{ route('reports.daily') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-day me-2"></i>Daily Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.weekly') ? 'active' : '' }}"
                   href="{{ route('reports.weekly') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-week me-2"></i>Weekly Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.monthly') ? 'active' : '' }}"
                   href="{{ route('reports.monthly') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-alt me-2"></i>Monthly Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.export') ? 'active' : '' }}"
                   href="{{ route('reports.export') }}"
                   wire:navigate>
                    <i class="fas fa-file-export me-2"></i>Export Data
                </a>
            </div>

            <!-- Settings -->
            <a class="nav-link has-submenu {{ request()->routeIs('settings.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               href="#settingsSubmenu"
               role="button"
               aria-expanded="{{ request()->routeIs('settings.*') ? 'true' : 'false' }}"
               aria-controls="settingsSubmenu">
                <span><i class="fas fa-cog me-2"></i>Settings</span>
            </a>
            <div class="collapse {{ request()->routeIs('settings.*') ? 'show' : '' }}" id="settingsSubmenu">
                <a class="nav-link {{ request()->routeIs('settings.store') ? 'active' : '' }}"
                   href="{{ route('settings.store') }}"
                   wire:navigate>
                    <i class="fas fa-store me-2"></i>Store Settings
                </a>
                <a class="nav-link {{ request()->routeIs('settings.users') ? 'active' : '' }}"
                   href="{{ route('settings.users') }}"
                   wire:navigate>
                    <i class="fas fa-users-cog me-2"></i>User Management
                </a>
                <a class="nav-link {{ request()->routeIs('settings.printer') ? 'active' : '' }}"
                   href="{{ route('settings.printer') }}"
                   wire:navigate>
                    <i class="fas fa-print me-2"></i>Printer Settings
                </a>
                <a class="nav-link {{ request()->routeIs('settings.backup') ? 'active' : '' }}"
                   href="{{ route('settings.backup') }}"
                   wire:navigate>
                    <i class="fas fa-database me-2"></i>Backup & Restore
                </a>
            </div>

            <!-- Additional Menu Items for Testing Scroll -->
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-bell me-2"></i>Notifications</span>
            </a>
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-question-circle me-2"></i>Help & Support</span>
            </a>
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-info-circle me-2"></i>About</span>
            </a>

            <hr class="text-white-50">
            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                @csrf
                <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
            </form>
        </nav>
    </div>

        <!-- Main Content -->
        <div class="main-content">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary sidebar-toggle me-3 d-md-none" type="button" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="navbar-brand">@yield('page-title', 'Dashboard')</span>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ Auth::user()->name ?? Auth::user()->username }}
                    </span>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="p-4">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            {{ $slot }}
        </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    @livewireScripts

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }



        // Handle sidebar toggle for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');

            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                const sidebar = document.querySelector('.sidebar');
                const sidebarToggle = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target) &&
                    sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                const sidebar = document.querySelector('.sidebar');
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                }
            });

            // Initialize Bootstrap collapse for submenus
            initializeCollapseMenus();
        });

        // Function to initialize collapse menus
        function initializeCollapseMenus() {
            // Wait for Bootstrap to be available
            if (typeof bootstrap === 'undefined') {
                setTimeout(initializeCollapseMenus, 100);
                return;
            }

            // Re-initialize Bootstrap collapse components
            const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
            collapseElements.forEach(function(element) {
                // Remove any existing Bootstrap collapse instance
                const existingCollapse = bootstrap.Collapse.getInstance(element);
                if (existingCollapse) {
                    existingCollapse.dispose();
                }

                // Create new Bootstrap collapse instance
                const targetSelector = element.getAttribute('href') || element.getAttribute('data-bs-target');
                const targetElement = document.querySelector(targetSelector);

                if (targetElement) {
                    new bootstrap.Collapse(targetElement, {
                        toggle: false
                    });
                }
            });
        }

        // Re-initialize after Livewire navigation
        document.addEventListener('livewire:navigated', function() {
            initializeCollapseMenus();
        });

        // Fallback manual toggle function
        function manualToggleSubmenu(element) {
            const targetSelector = element.getAttribute('href') || element.getAttribute('data-bs-target');
            const targetElement = document.querySelector(targetSelector);

            if (targetElement) {
                const isShown = targetElement.classList.contains('show');

                // Close all other submenus
                document.querySelectorAll('.collapse.show').forEach(function(openSubmenu) {
                    if (openSubmenu !== targetElement) {
                        openSubmenu.classList.remove('show');
                        // Update aria-expanded for corresponding toggle
                        const correspondingToggle = document.querySelector(`[href="#${openSubmenu.id}"], [data-bs-target="#${openSubmenu.id}"]`);
                        if (correspondingToggle) {
                            correspondingToggle.setAttribute('aria-expanded', 'false');
                        }
                    }
                });

                // Toggle current submenu
                if (isShown) {
                    targetElement.classList.remove('show');
                    element.setAttribute('aria-expanded', 'false');
                } else {
                    targetElement.classList.add('show');
                    element.setAttribute('aria-expanded', 'true');
                }
            }
        }

        // Add click event listeners as fallback
        document.addEventListener('click', function(e) {
            const collapseToggle = e.target.closest('[data-bs-toggle="collapse"]');
            if (collapseToggle) {
                // Let Bootstrap handle it first, but add fallback
                setTimeout(function() {
                    const targetSelector = collapseToggle.getAttribute('href') || collapseToggle.getAttribute('data-bs-target');
                    const targetElement = document.querySelector(targetSelector);

                    // If Bootstrap didn't handle it, use manual toggle
                    if (targetElement && !targetElement.classList.contains('collapsing')) {
                        const bootstrapInstance = bootstrap.Collapse.getInstance(targetElement);
                        if (!bootstrapInstance) {
                            manualToggleSubmenu(collapseToggle);
                        }
                    }
                }, 50);
            }
        });
        });
    </script>

    @stack('scripts')
</body>
</html>
