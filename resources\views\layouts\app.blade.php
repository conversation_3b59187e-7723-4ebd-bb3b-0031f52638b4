<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'ASICS')</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    @livewireStyles
    <style>
        .sidebar {
            min-height: 100vh;
            max-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        .sidebar .nav-link.has-submenu::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            transition: transform 0.3s ease;
            transform: rotate(0deg);
        }
        .sidebar .nav-link.has-submenu.collapsed::after {
            transform: rotate(-90deg);
        }
        .sidebar .nav-link.has-submenu:not(.collapsed)::after {
            transform: rotate(0deg);
        }
        .sidebar .submenu {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin: 5px 0;
            overflow: hidden;
            max-height: 0;
            transition: max-height 0.4s ease-in-out;
            opacity: 0;
        }

        .sidebar .submenu.show {
            max-height: 300px;
            opacity: 1;
            transition: max-height 0.4s ease-in-out, opacity 0.2s ease-in-out 0.1s;
        }
        .sidebar .submenu .nav-link {
            padding: 8px 20px 8px 40px;
            margin: 1px 0;
            font-size: 0.9em;
        }
        .sidebar .submenu .nav-link:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateX(3px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .sidebar-toggle {
            display: none;
        }

        /* Ensure full width layout */
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .layout-wrapper {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        /* Ensure content takes full width */
        .container-fluid {
            max-width: none !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        @media (max-width: 768px) {
            .layout-wrapper {
                flex-direction: column;
            }
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                z-index: 1050;
                transform: translateX(-100%);
                transition: transform 0.3s;
                width: 250px !important;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                width: 100%;
            }
            .sidebar-toggle {
                display: block;
            }
        }
    </style>
    @stack('styles')
</head>
<body>
    <div class="layout-wrapper">
        <!-- Sidebar -->
        <div class="sidebar" style="width: 250px; flex-shrink: 0;">
        <div class="p-3 text-center text-white">
            <h4><i class="fas fa-running me-2"></i>ASICS</h4>
            <small>Welcome, {{ Auth::user()->name ?? Auth::user()->username }}</small>
        </div>
        <nav class="nav flex-column px-3 pb-4">
            <!-- Dashboard -->
            <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
               href="{{ route('dashboard') }}"
               wire:navigate>
                <span><i class="fas fa-tachometer-alt me-2"></i>Dashboard</span>
            </a>

            <!-- Inventory Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? 'active' : '' }} {{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? '' : 'collapsed' }}"
               href="javascript:void(0)"
               onclick="toggleSubmenu('inventorySubmenu', this)">
                <span><i class="fas fa-boxes me-2"></i>Inventory</span>
            </a>
            <div class="submenu {{ request()->routeIs('products.*') || request()->routeIs('inventory.*') ? 'show' : '' }}" id="inventorySubmenu">
                <a class="nav-link {{ request()->routeIs('products.index') ? 'active' : '' }}"
                   href="{{ route('products.index') }}"
                   wire:navigate>
                    <i class="fas fa-box me-2"></i>Products
                </a>
                <a class="nav-link {{ request()->routeIs('products.create') ? 'active' : '' }}"
                   href="{{ route('products.create') }}"
                   wire:navigate>
                    <i class="fas fa-plus me-2"></i>Add Product
                </a>
                <a class="nav-link {{ request()->routeIs('inventory.stock') ? 'active' : '' }}"
                   href="{{ route('inventory.stock') }}"
                   wire:navigate>
                    <i class="fas fa-warehouse me-2"></i>Stock Management
                </a>
                <a class="nav-link {{ request()->routeIs('inventory.reports') ? 'active' : '' }}"
                   href="{{ route('inventory.reports') }}"
                   wire:navigate>
                    <i class="fas fa-chart-line me-2"></i>Stock Reports
                </a>
            </div>

            <!-- Sales Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('sales.*') ? 'active' : '' }} {{ request()->routeIs('sales.*') ? '' : 'collapsed' }}"
               href="javascript:void(0)"
               onclick="toggleSubmenu('salesSubmenu', this)">
                <span><i class="fas fa-shopping-cart me-2"></i>Sales</span>
            </a>
            <div class="submenu {{ request()->routeIs('sales.*') ? 'show' : '' }}" id="salesSubmenu">
                <a class="nav-link {{ request()->routeIs('sales.pos') ? 'active' : '' }}"
                   href="{{ route('sales.pos') }}"
                   wire:navigate>
                    <i class="fas fa-cash-register me-2"></i>Point of Sale
                </a>
                <a class="nav-link {{ request()->routeIs('sales.history') ? 'active' : '' }}"
                   href="{{ route('sales.history') }}"
                   wire:navigate>
                    <i class="fas fa-receipt me-2"></i>Sales History
                </a>
                <a class="nav-link {{ request()->routeIs('sales.returns') ? 'active' : '' }}"
                   href="{{ route('sales.returns') }}"
                   wire:navigate>
                    <i class="fas fa-undo me-2"></i>Returns
                </a>
                <a class="nav-link {{ request()->routeIs('sales.reports') ? 'active' : '' }}"
                   href="{{ route('sales.reports') }}"
                   wire:navigate>
                    <i class="fas fa-chart-bar me-2"></i>Sales Reports
                </a>
            </div>

            <!-- Customer Management -->
            <a class="nav-link has-submenu {{ request()->routeIs('customers.*') ? 'active' : '' }} {{ request()->routeIs('customers.*') ? '' : 'collapsed' }}"
               href="javascript:void(0)"
               onclick="toggleSubmenu('customersSubmenu', this)">
                <span><i class="fas fa-users me-2"></i>Customers</span>
            </a>
            <div class="submenu {{ request()->routeIs('customers.*') ? 'show' : '' }}" id="customersSubmenu">
                <a class="nav-link {{ request()->routeIs('customers.create') ? 'active' : '' }}"
                   href="{{ route('customers.create') }}"
                   wire:navigate>
                    <i class="fas fa-user-plus me-2"></i>Add Customer
                </a>
                <a class="nav-link {{ request()->routeIs('customers.index') ? 'active' : '' }}"
                   href="{{ route('customers.index') }}"
                   wire:navigate>
                    <i class="fas fa-list me-2"></i>Customer List
                </a>
                <a class="nav-link {{ request()->routeIs('customers.loyalty') ? 'active' : '' }}"
                   href="{{ route('customers.loyalty') }}"
                   wire:navigate>
                    <i class="fas fa-star me-2"></i>Loyalty Program
                </a>
            </div>

            <!-- Reports -->
            <a class="nav-link has-submenu {{ request()->routeIs('reports.*') ? 'active' : '' }} {{ request()->routeIs('reports.*') ? '' : 'collapsed' }}"
               href="javascript:void(0)"
               onclick="toggleSubmenu('reportsSubmenu', this)">
                <span><i class="fas fa-chart-pie me-2"></i>Reports</span>
            </a>
            <div class="submenu {{ request()->routeIs('reports.*') ? 'show' : '' }}" id="reportsSubmenu">
                <a class="nav-link {{ request()->routeIs('reports.daily') ? 'active' : '' }}"
                   href="{{ route('reports.daily') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-day me-2"></i>Daily Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.weekly') ? 'active' : '' }}"
                   href="{{ route('reports.weekly') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-week me-2"></i>Weekly Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.monthly') ? 'active' : '' }}"
                   href="{{ route('reports.monthly') }}"
                   wire:navigate>
                    <i class="fas fa-calendar-alt me-2"></i>Monthly Reports
                </a>
                <a class="nav-link {{ request()->routeIs('reports.export') ? 'active' : '' }}"
                   href="{{ route('reports.export') }}"
                   wire:navigate>
                    <i class="fas fa-file-export me-2"></i>Export Data
                </a>
            </div>

            <!-- Settings -->
            <a class="nav-link has-submenu {{ request()->routeIs('settings.*') ? 'active' : '' }} {{ request()->routeIs('settings.*') ? '' : 'collapsed' }}"
               href="#settingsSubmenu">
                <span><i class="fas fa-cog me-2"></i>Settings</span>
            </a>
            <div class="submenu {{ request()->routeIs('settings.*') ? 'show' : '' }}" id="settingsSubmenu">
                <a class="nav-link {{ request()->routeIs('settings.store') ? 'active' : '' }}"
                   href="{{ route('settings.store') }}"
                   wire:navigate>
                    <i class="fas fa-store me-2"></i>Store Settings
                </a>
                <a class="nav-link {{ request()->routeIs('settings.users') ? 'active' : '' }}"
                   href="{{ route('settings.users') }}"
                   wire:navigate>
                    <i class="fas fa-users-cog me-2"></i>User Management
                </a>
                <a class="nav-link {{ request()->routeIs('settings.printer') ? 'active' : '' }}"
                   href="{{ route('settings.printer') }}"
                   wire:navigate>
                    <i class="fas fa-print me-2"></i>Printer Settings
                </a>
                <a class="nav-link {{ request()->routeIs('settings.backup') ? 'active' : '' }}"
                   href="{{ route('settings.backup') }}"
                   wire:navigate>
                    <i class="fas fa-database me-2"></i>Backup & Restore
                </a>
            </div>

            <!-- Additional Menu Items for Testing Scroll -->
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-bell me-2"></i>Notifications</span>
            </a>
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-question-circle me-2"></i>Help & Support</span>
            </a>
            <a class="nav-link" href="#" wire:navigate>
                <span><i class="fas fa-info-circle me-2"></i>About</span>
            </a>

            <hr class="text-white-50">
            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                @csrf
                <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </button>
            </form>
        </nav>
    </div>

        <!-- Main Content -->
        <div class="main-content">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary sidebar-toggle me-3 d-md-none" type="button" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="navbar-brand">@yield('page-title', 'Dashboard')</span>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ Auth::user()->name ?? Auth::user()->username }}
                    </span>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="p-4 flex-grow-1">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            {{ $slot }}
        </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    @livewireScripts

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // Handle submenu toggle
        document.addEventListener('DOMContentLoaded', function() {
            // Function to toggle submenu
            function toggleSubmenu(clickedToggle) {
                const targetId = clickedToggle.getAttribute('href').substring(1);
                const targetSubmenu = document.getElementById(targetId);

                if (!targetSubmenu) return;

                const isCurrentlyShown = targetSubmenu.classList.contains('show');

                // Close all submenus first
                document.querySelectorAll('.submenu').forEach(function(submenu) {
                    submenu.classList.remove('show');
                });

                // Reset all toggles to collapsed state
                document.querySelectorAll('.nav-link.has-submenu').forEach(function(toggle) {
                    toggle.classList.add('collapsed');
                });

                // If the clicked submenu was not shown, show it
                if (!isCurrentlyShown) {
                    targetSubmenu.classList.add('show');
                    clickedToggle.classList.remove('collapsed');
                }
            }

            // Add click event listeners to all submenu toggles
            document.querySelectorAll('.nav-link.has-submenu').forEach(function(toggle) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSubmenu(this);
                });
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                const sidebar = document.querySelector('.sidebar');
                const sidebarToggle = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !sidebarToggle.contains(e.target) &&
                    sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                const sidebar = document.querySelector('.sidebar');
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
