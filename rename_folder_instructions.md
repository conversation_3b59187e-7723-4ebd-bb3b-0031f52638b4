# Folder Rename Instructions

## Steps to rename the project folder from `pos_kasir` to `asics`:

### 1. Close all applications using the folder
- Close your IDE/editor
- Stop any running Laravel development server
- Close any terminal windows in the project directory

### 2. Rename the folder
Navigate to the parent directory (`d:\dev\laravel\`) and rename the folder:

**Option A: Using Windows Explorer**
1. Open Windows Explorer
2. Navigate to `d:\dev\laravel\`
3. Right-click on `pos_kasir` folder
4. Select "Rename"
5. Change name to `asics`

**Option B: Using Command Prompt**
```cmd
cd d:\dev\laravel\
ren pos_kasir asics
```

**Option C: Using PowerShell**
```powershell
cd d:\dev\laravel\
Rename-Item -Path "pos_kasir" -NewName "asics"
```

### 3. Update your development environment
After renaming the folder:

1. **Update Laragon (if using)**
   - Open Laragon
   - Remove the old project from the list
   - Add the new `asics` folder to Laragon

2. **Update your IDE/Editor**
   - Open your IDE
   - Close the old project
   - Open the new `asics` folder

3. **Update any bookmarks or shortcuts**
   - Update any browser bookmarks
   - Update any desktop shortcuts
   - Update any IDE project bookmarks

### 4. Verify the changes
1. Navigate to the new folder: `d:\dev\laravel\asics\`
2. Run `php artisan serve` to test the application
3. Check that all references have been updated correctly

## Files Already Updated

The following files have been automatically updated with the new branding:

✅ `.env` - APP_NAME and DB_DATABASE updated to "asics"
✅ `composer.json` - Project name and description updated
✅ `resources/views/layouts/app.blade.php` - Title and branding updated
✅ `resources/views/layouts/guest.blade.php` - Title updated
✅ `resources/views/livewire/auth/login.blade.php` - Branding updated
✅ `app/Livewire/Dashboard.php` - Page title updated
✅ `app/Livewire/Products/ProductList.php` - Page title updated
✅ `app/Livewire/Products/ProductForm.php` - Page title updated

## Database Update Required

Don't forget to:
1. Create a new database named `asics` in SQL Server
2. Run migrations: `php artisan migrate`
3. Execute the stored procedures from `database/sql/stored_procedures.sql`

## Notes
- The icon has been changed from cash register (`fa-cash-register`) to running (`fa-running`) to match ASICS branding
- All page titles now show "ASICS" instead of "POS Kasir"
- The application name in the environment is now "ASICS"
