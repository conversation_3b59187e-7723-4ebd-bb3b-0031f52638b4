-- SQL Server Stored Procedures for POS Kasir Application
-- Execute these procedures in your SQL Server database

-- 1. Create the login check stored procedure
CREATE OR ALTER PROCEDURE sp_CheckLogin
    @Username NVARCHAR(255),
    @Password NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId INT = NULL;
    DECLARE @StoredPassword NVARCHAR(255);
    DECLARE @Salt NVARCHAR(36);
    DECLARE @HashedPassword NVARCHAR(255);
    DECLARE @IsActive BIT;
    
    -- Get user information
    SELECT 
        @UserId = id,
        @StoredPassword = password,
        @Salt = salt,
        @IsActive = is_active
    FROM users 
    WHERE username = @Username AND is_active = 1;
    
    -- If user exists, verify password
    IF @UserId IS NOT NULL
    BEGIN
        -- Hash the provided password with salt using SHA1
        SET @HashedPassword = CONVERT(NVARCHAR(255), HASHBYTES('SHA1', @Password + CAST(@Salt AS NVARCHAR(36))), 2);
        
        -- Check if passwords match
        IF @HashedPassword = @StoredPassword
        BEGIN
            SELECT 
                1 as IsValid,
                @UserId as UserId,
                'Login successful' as Message;
        END
        ELSE
        BEGIN
            SELECT 
                0 as IsValid,
                NULL as UserId,
                'Invalid password' as Message;
        END
    END
    ELSE
    BEGIN
        SELECT 
            0 as IsValid,
            NULL as UserId,
            'User not found or inactive' as Message;
    END
END;
GO

-- 2. Create a procedure to create new user with hashed password
CREATE OR ALTER PROCEDURE sp_CreateUser
    @Username NVARCHAR(255),
    @Name NVARCHAR(255),
    @Email NVARCHAR(255),
    @Password NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Salt NVARCHAR(36);
    DECLARE @HashedPassword NVARCHAR(255);
    DECLARE @UserId INT;
    
    -- Generate salt (you can use NEWID() for uniqueness)
    SET @Salt = REPLACE(CAST(NEWID() AS NVARCHAR(36)), '-', '');
    
    -- Hash password with salt
    SET @HashedPassword = CONVERT(NVARCHAR(255), HASHBYTES('SHA1', @Password + @Salt), 2);
    
    -- Insert new user
    INSERT INTO users (username, name, email, password, salt, is_active, created_at, updated_at)
    VALUES (@Username, @Name, @Email, @HashedPassword, @Salt, 1, GETDATE(), GETDATE());
    
    SET @UserId = SCOPE_IDENTITY();
    
    SELECT 
        @UserId as UserId,
        'User created successfully' as Message;
END;
GO

-- 3. Sample data insertion
-- First, let's create a sample user (password: 'admin123')
EXEC sp_CreateUser 
    @Username = 'admin',
    @Name = 'Administrator',
    @Email = '<EMAIL>',
    @Password = 'admin123';

-- Insert sample products
INSERT INTO products (name, description, price, stock, category, barcode, is_active, created_at, updated_at)
VALUES 
    ('Coca Cola 330ml', 'Minuman ringan berkarbonasi', 5000.00, 100, 'Beverages', '1234567890', 1, GETDATE(), GETDATE()),
    ('Indomie Goreng', 'Mie instan rasa ayam bawang', 3500.00, 200, 'Food', '2345678901', 1, GETDATE(), GETDATE()),
    ('Aqua 600ml', 'Air mineral dalam kemasan', 3000.00, 150, 'Beverages', '3456789012', 1, GETDATE(), GETDATE()),
    ('Chitato Sapi Panggang', 'Keripik kentang rasa sapi panggang', 8000.00, 50, 'Snacks', '4567890123', 1, GETDATE(), GETDATE()),
    ('Teh Botol Sosro', 'Minuman teh dalam kemasan', 4000.00, 80, 'Beverages', '5678901234', 1, GETDATE(), GETDATE());

-- 4. Procedure to get low stock products
CREATE OR ALTER PROCEDURE sp_GetLowStockProducts
    @MinStock INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        id,
        name,
        category,
        stock,
        price,
        barcode
    FROM products 
    WHERE stock <= @MinStock AND is_active = 1
    ORDER BY stock ASC;
END;
GO

-- 5. Procedure to update product stock
CREATE OR ALTER PROCEDURE sp_UpdateProductStock
    @ProductId INT,
    @NewStock INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE products 
    SET stock = @NewStock, updated_at = GETDATE()
    WHERE id = @ProductId;
    
    SELECT 
        @@ROWCOUNT as RowsAffected,
        'Stock updated successfully' as Message;
END;
GO

-- 6. Procedure to search products
CREATE OR ALTER PROCEDURE sp_SearchProducts
    @SearchTerm NVARCHAR(255) = '',
    @Category NVARCHAR(100) = '',
    @IsActive BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        id,
        name,
        description,
        price,
        stock,
        category,
        barcode,
        is_active,
        created_at,
        updated_at
    FROM products 
    WHERE 
        (@SearchTerm = '' OR name LIKE '%' + @SearchTerm + '%' OR barcode LIKE '%' + @SearchTerm + '%')
        AND (@Category = '' OR category = @Category)
        AND (@IsActive IS NULL OR is_active = @IsActive)
    ORDER BY created_at DESC;
END;
GO

PRINT 'All stored procedures created successfully!';
PRINT 'Sample admin user created with username: admin, password: admin123';
PRINT 'Sample products inserted successfully!';
