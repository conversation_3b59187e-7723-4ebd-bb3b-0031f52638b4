<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;

class DashboardController extends Controller
{
    /**
     * Show the dashboard
     */
    public function index()
    {
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::where('stock', '<=', 10)->count();
        $totalValue = Product::sum(\DB::raw('price * stock'));

        $recentProducts = Product::latest()->take(5)->get();

        return view('dashboard', compact(
            'totalProducts',
            'activeProducts',
            'lowStockProducts',
            'totalValue',
            'recentProducts'
        ));
    }
}
